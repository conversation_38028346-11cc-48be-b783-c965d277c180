body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background: #f9fafb;
  color: #111;
  height: 100vh;
  overflow: hidden;
}

.layout {
  display: flex;
  height: 100vh;
  width: 100vw;
}

/* SIDEBARS */
.sidebar {
  width: 260px;
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
  box-sizing: border-box;
}

.sidebar.right {
  border-right: none;
  border-left: 1px solid #e5e7eb;
}

.sidebar h3 {
  margin-top: 1rem;
  color: #646cff;
  font-size: 1.2rem;
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  margin-bottom: 0.5rem;
}

.sidebar button {
  background: none;
  border: none;
  font-size: 1rem;
  color: #111;
  text-align: left;
  width: 100%;
  cursor: pointer;
  color: #333;
  padding: 0.4rem 0;
}

.sidebar button.active {
  font-weight: bold;
  color: #111;
}

.columns-list {
  padding-left: 20px;
}
.columns-list li {
  margin-bottom: 5px;
}

/* MAIN AREA */
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* CHAT WINDOW */
.chat-window {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  box-sizing: border-box;
}

.message {
  max-width: 75%;
  padding: 1rem;
  border-radius: 8px;
  white-space: pre-wrap;
}

.message.user {
  background: #e0e7ff;
  align-self: flex-end;
}

.message.assistant {
  background: #f1f5f9;
  align-self: flex-start;
}

/* PROMPT BAR */
.prompt-bar {
  display: flex;
  padding: 1rem;
  background: #f9fafb;
  position: relative;
  align-items: flex-end;
}

.prompt-bar .textarea-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: flex-end;
}

.prompt-bar textarea {
  flex: 1;
  resize: none;
  padding: 0.75rem 3rem 0.75rem 0.75rem;
  font-size: 1rem;
  border: 1px solid #f4aaee;
  color: #620dca;
  border-radius: 8px;
  max-height: 120px;
  background: #f9fafb;
  min-height: 50px;
}

.prompt-bar button {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: #10a37f;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* SEARCH INPUT */
.sidebar.right input[type="text"] {
  width: 90%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #f4aaee;
  border-radius: 4px;
  background-color: #f6d0d0;
  color: #620dca;
  font-size: 1rem;
}

/* WELCOME SCREEN */
.welcome-screen {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  color: #620dca;
}

.prompt-bar.centered {
  width: 60%;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .prompt-bar.centered {
    width: 90%;
  }

  .sidebar.left,
  .sidebar.right {
    display: none;
  }

  .main {
    width: 100vw;
  }

  .prompt-bar {
    padding: 0.75rem;
  }

  .chat-window {
    padding: 1rem;
  }
}
