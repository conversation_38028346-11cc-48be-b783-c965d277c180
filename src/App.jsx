import { useState, useEffect } from 'react';
import './App.css';
import { useChat, useDatabase } from './hooks/useApi';

function App() {
  // API hooks
  const {
    loading: chatLoading,
    error: chatError,
    chats,
    currentChat,
    messages,
    setCurrentChat,
    loadChats,
    createChat,
    loadMessages,
    sendMessage,
    updateChatTitle
  } = useChat();

  const {
    loading: dbLoading,
    error: dbError,
    tables: dbTables,
    loadTables,
    executeQuery
  } = useDatabase();

  // Local state
  const [prompt, setPrompt] = useState('');
  const [search, setSearch] = useState('');
  const [selectedTable, setSelectedTable] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load initial data
  useEffect(() => {
    loadChats();
    loadTables();
  }, [loadChats, loadTables]);

  const createNewChat = async () => {
    try {
      const newChat = await createChat('New Chat');
      // Don't select the chat yet — wait until user sends the first prompt
      setCurrentChat(null);
    } catch (error) {
      console.error('Failed to create new chat:', error);
    }
  };

  const handleSend = async () => {
    if (!prompt.trim() || isSubmitting) return;

    setIsSubmitting(true);
    const userMessage = prompt.trim();
    setPrompt('');

    try {
      let chatId = currentChat?.id;

      // If no chat is selected, create a new one
      if (!chatId) {
        const newChat = await createChat('New Chat');
        chatId = newChat.id;
        setCurrentChat(newChat);
      }

      // Send message to backend (includes AI response)
      await sendMessage(chatId, userMessage);

      // Update chat title after second user message
      const chatMessages = messages[chatId] || [];
      const userMessages = chatMessages.filter(m => m.role === 'user');
      if (userMessages.length === 1) { // This will be the second user message after the API call
        const words = userMessage.split(' ').slice(0, 3).join(' ');
        await updateChatTitle(chatId, words);
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      // Restore the prompt on error
      setPrompt(userMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const displayMessages = currentChat ? messages[currentChat.id] || [] : [];

  return (
    <div className="layout">
      {/* Error Display */}
      {(chatError || dbError) && (
        <div className="error-banner">
          {chatError && <p>Chat Error: {chatError}</p>}
          {dbError && <p>Database Error: {dbError}</p>}
        </div>
      )}
      {/* Left Sidebar */}
      <aside className="sidebar left">
        <button onClick={createNewChat}>+ New Chat</button>
        <h3>Chats</h3>
        <ul>
          {chats.map(chat => (
            <li key={chat.id}>
              <button
                className={currentChat?.id === chat.id ? 'active' : ''}
                onClick={() => {
                  setCurrentChat(chat);
                  loadMessages(chat.id);
                }}
              >
                {chat.title}
              </button>
            </li>
          ))}
        </ul>
      </aside>

      {/* Main Area */}
      <main className="main">
        {currentChat && displayMessages.length > 0 ? (
          <>
            <div className="chat-window">
              {displayMessages.map((msg, idx) => (
                <div key={idx} className={`message ${msg.role}`}>
                  {msg.content}
                </div>
              ))}
            </div>
            <div className="prompt-bar">
              <div className="textarea-wrapper">
                <textarea
                  value={prompt}
                  placeholder="Type your message..."
                  onChange={e => setPrompt(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />
                <button onClick={handleSend} disabled={isSubmitting}>
                  {isSubmitting ? '...' : '↑'}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="welcome-screen">
            <h2>Hello, What's on your mind?</h2>
            <div className="prompt-bar centered">
              <div className="textarea-wrapper">
                <textarea
                  value={prompt}
                  placeholder="Type your message..."
                  onChange={e => setPrompt(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />
                <button onClick={handleSend} disabled={isSubmitting}>
                  {isSubmitting ? '...' : '↑'}
                </button>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Right Sidebar */}
      <aside className="sidebar right">
        <h3>DB Tables</h3>
        <input
          type="text"
          placeholder="Search tables..."
          value={search}
          onChange={e => setSearch(e.target.value)}
        />
        <ul>
          {dbTables
            .filter(table => table.name.toLowerCase().includes(search.toLowerCase()))
            .map(table => (
              <li key={table.name}>
                <button
                  className={selectedTable === table.name ? 'active' : ''}
                  onClick={() =>
                    setSelectedTable(selectedTable === table.name ? null : table.name)
                  }
                >
                  {table.name}
                </button>
                {selectedTable === table.name && (
                  <ul className="columns-list">
                    {table.columns?.map(col => (
                      <li key={col.name || col}>{col.name || col}</li>
                    )) || <li>Loading columns...</li>}
                  </ul>
                )}
              </li>
            ))}
        </ul>
      </aside>
    </div>
  );
}

export default App;
