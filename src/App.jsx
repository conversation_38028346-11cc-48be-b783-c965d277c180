import { useState, useEffect } from 'react';
import './App.css';
import { useChat, useDatabase } from './hooks/useApi';

function App() {
  // API hooks
  const {
    loading: chatLoading,
    error: chatError,
    chats,
    currentChat,
    messages,
    setCurrentChat,
    loadChats,
    createChat,
    loadMessages,
    sendMessage,
    updateChatTitle,
    deleteChat,
    archiveChat,
    unarchiveChat
  } = useChat();

  const {
    loading: dbLoading,
    error: dbError,
    tables: dbTables,
    loadTables,
    executeQuery
  } = useDatabase();

  // Local state
  const [prompt, setPrompt] = useState('');
  const [search, setSearch] = useState('');
  const [selectedTable, setSelectedTable] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Chat options state
  const [openDropdown, setOpenDropdown] = useState(null);
  const [editingChat, setEditingChat] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [showArchived, setShowArchived] = useState(false);

  // Load initial data
  useEffect(() => {
    loadChats();
    loadTables();
  }, [loadChats, loadTables]);

  // Restore selected chat when chats are loaded
  useEffect(() => {
    const savedChatId = localStorage.getItem('selectedChatId');
    if (savedChatId && chats.length > 0 && !currentChat) {
      const savedChat = chats.find(chat => chat.id === savedChatId);
      if (savedChat) {
        setCurrentChat(savedChat);
        loadMessages(savedChatId);
      } else {
        // If saved chat doesn't exist anymore, clear the localStorage
        localStorage.removeItem('selectedChatId');
      }
    }
  }, [chats, currentChat, setCurrentChat, loadMessages]);

  // Save selected chat to localStorage whenever it changes
  useEffect(() => {
    if (currentChat?.id) {
      localStorage.setItem('selectedChatId', currentChat.id);
    } else {
      localStorage.removeItem('selectedChatId');
    }
  }, [currentChat]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.chat-options')) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Handle shared chat URLs
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const sharedChatId = urlParams.get('chat');

    if (sharedChatId && chats.length > 0) {
      const sharedChat = chats.find(chat => chat.id === sharedChatId);
      if (sharedChat) {
        setCurrentChat(sharedChat);
        loadMessages(sharedChatId);
        // Clear the URL parameter after loading
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    }
  }, [chats, setCurrentChat, loadMessages]);

  const createNewChat = async () => {
    try {
      // Create with a timestamp-based title that will be updated on first message
      const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      await createChat(`Chat ${timestamp}`);
      // Don't select the chat yet — wait until user sends the first prompt
      setCurrentChat(null);
    } catch (error) {
      console.error('Failed to create new chat:', error);
    }
  };

  // Chat options handlers
  const handleRenameChat = (chat) => {
    setEditingChat(chat.id);
    setEditTitle(chat.title);
    setOpenDropdown(null);
  };

  const handleSaveRename = async () => {
    if (editTitle.trim() && editingChat) {
      try {
        await updateChatTitle(editingChat, editTitle.trim());
        setEditingChat(null);
        setEditTitle('');
      } catch (error) {
        console.error('Failed to rename chat:', error);
      }
    }
  };

  const handleCancelRename = () => {
    setEditingChat(null);
    setEditTitle('');
  };

  const handleDeleteChat = async (chatId) => {
    try {
      await deleteChat(chatId);
      setShowDeleteConfirm(null);
      setOpenDropdown(null);
      // Clear localStorage if deleted chat was selected
      if (currentChat?.id === chatId) {
        localStorage.removeItem('selectedChatId');
      }
    } catch (error) {
      console.error('Failed to delete chat:', error);
    }
  };

  const handleArchiveChat = async (chatId) => {
    try {
      await archiveChat(chatId);
      setOpenDropdown(null);
      // Clear localStorage if archived chat was selected
      if (currentChat?.id === chatId) {
        localStorage.removeItem('selectedChatId');
      }
    } catch (error) {
      console.error('Failed to archive chat:', error);
    }
  };

  const handleUnarchiveChat = async (chatId) => {
    try {
      await unarchiveChat(chatId);
      setOpenDropdown(null);
    } catch (error) {
      console.error('Failed to unarchive chat:', error);
    }
  };

  const toggleDropdown = (chatId) => {
    setOpenDropdown(openDropdown === chatId ? null : chatId);
  };

  const handleShareChat = async (chat) => {
    try {
      const chatUrl = `${window.location.origin}${window.location.pathname}?chat=${chat.id}`;

      if (navigator.share) {
        // Use native share API if available
        await navigator.share({
          title: `Chat: ${chat.title}`,
          text: `Check out this database chat conversation`,
          url: chatUrl
        });
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(chatUrl);
        alert('Chat link copied to clipboard!');
      }
      setOpenDropdown(null);
    } catch (error) {
      console.error('Failed to share chat:', error);
      // Fallback: just copy to clipboard
      try {
        await navigator.clipboard.writeText(`${window.location.origin}${window.location.pathname}?chat=${chat.id}`);
        alert('Chat link copied to clipboard!');
      } catch (clipboardError) {
        console.error('Failed to copy to clipboard:', clipboardError);
      }
    }
  };

  // Helper function to generate chat title from message
  const generateChatTitle = (message) => {
    // Remove common question words and clean up the message
    const cleanMessage = message
      .toLowerCase()
      .replace(/^(show me|get me|find|select|what|how|when|where|why|can you|please|could you|give me|tell me|list|display)\s+/i, '')
      .replace(/\?+$/, '')
      .trim();

    // Check for greeting/welcome messages first - but only for very simple greetings
    const simpleGreetings = ['hi', 'hello', 'hey', 'there', 'hy', 'hii'];
    const messageLower = message.toLowerCase().trim();

    // Only create "Welcome Chat" for single-word greetings
    if (simpleGreetings.includes(messageLower) || messageLower === 'hi there' || messageLower === 'hello there') {
      return 'Welcome Chat';
    }

    // Database-specific patterns for better titles
    const dbPatterns = [
      { pattern: /users?\s+(?:with|where|having|from)/i, title: 'User Query' },
      { pattern: /orders?\s+(?:with|where|having|from)/i, title: 'Order Analysis' },
      { pattern: /products?\s+(?:with|where|having|from)/i, title: 'Product Search' },
      { pattern: /sales?\s+(?:data|report|analysis)/i, title: 'Sales Report' },
      { pattern: /revenue|profit|income/i, title: 'Financial Analysis' },
      { pattern: /customers?\s+(?:who|that|with)/i, title: 'Customer Analysis' },
      { pattern: /top\s+\d+|highest|lowest|best|worst/i, title: 'Ranking Query' },
      { pattern: /count|total|sum|average|avg/i, title: 'Aggregate Query' },
      { pattern: /join|relationship|connect/i, title: 'Data Relationship' },
      { pattern: /between\s+.*\s+and/i, title: 'Range Query' }
    ];

    // Check for database patterns first
    for (const { pattern, title } of dbPatterns) {
      if (pattern.test(message)) {
        return title;
      }
    }

    // Extract meaningful keywords
    const stopWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'run', 'too', 'use', 'way', 'she', 'many', 'some', 'time', 'from', 'with', 'that', 'this', 'have', 'will', 'been', 'were', 'said', 'each', 'which', 'their', 'would', 'there', 'could', 'other'];

    const words = cleanMessage.split(' ').filter(word =>
      word.length > 2 && !stopWords.includes(word.toLowerCase())
    );

    // Take first 3-4 meaningful words
    const title = words.slice(0, 3).join(' ');

    // Capitalize first letter and return
    const finalTitle = title.charAt(0).toUpperCase() + title.slice(1);
    return finalTitle || 'Database Query';
  };

  const handleSend = async () => {
    if (!prompt.trim() || isSubmitting) return;

    setIsSubmitting(true);
    const userMessage = prompt.trim();
    setPrompt('');

    try {
      let chatId = currentChat?.id;
      let isNewChat = false;

      // If no chat is selected, create a new one with a generated title
      if (!chatId) {
        const generatedTitle = generateChatTitle(userMessage);
        const newChat = await createChat(generatedTitle);
        chatId = newChat.id;
        setCurrentChat(newChat);
        isNewChat = true;
      }

      // Send message to backend (includes AI response)
      await sendMessage(chatId, userMessage);

      // Update chat title based on message count and content
      const chatMessages = messages[chatId] || [];
      const userMessages = chatMessages.filter(m => m.role === 'user');

      if (!isNewChat) {
        // For existing chats, update title after first message if it's a generic title
        const isGenericTitle = currentChat?.title?.match(/^(New Chat|Chat \d{1,2}:\d{2})$/);

        if (userMessages.length === 0 && isGenericTitle) {
          const generatedTitle = generateChatTitle(userMessage);
          await updateChatTitle(chatId, generatedTitle);
        }
        // Update after second message to create a more comprehensive title
        else if (userMessages.length === 1) {
          const firstMessage = userMessages[0].content;
          const firstTopic = generateChatTitle(firstMessage);
          const secondTopic = generateChatTitle(userMessage);

          // Only combine if topics are meaningfully different
          if (firstTopic.toLowerCase() !== secondTopic.toLowerCase() &&
              !firstTopic.toLowerCase().includes(secondTopic.toLowerCase()) &&
              !secondTopic.toLowerCase().includes(firstTopic.toLowerCase())) {

            const combinedTitle = `${firstTopic} & ${secondTopic}`;
            // Keep title under 40 characters for better UI
            const finalTitle = combinedTitle.length > 40 ? firstTopic : combinedTitle;
            await updateChatTitle(chatId, finalTitle);
          }
        }
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      // Restore the prompt on error
      setPrompt(userMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const displayMessages = currentChat ? messages[currentChat.id] || [] : [];

  return (
    <div className="layout">
      {/* Error Display */}
      {(chatError || dbError) && (
        <div className="error-banner">
          {chatError && <p>Chat Error: {chatError}</p>}
          {dbError && <p>Database Error: {dbError}</p>}
        </div>
      )}
      {/* Left Sidebar */}
      <aside className="sidebar left">
        <button onClick={createNewChat}>+ New Chat</button>
        <div className="chats-header">
          <h3>Chats</h3>
          <button
            className={`archive-toggle ${showArchived ? 'active' : ''}`}
            onClick={() => setShowArchived(!showArchived)}
            title={showArchived ? 'Hide archived chats' : 'Show archived chats'}
          >
            {showArchived ? '�' : '�'}
          </button>
        </div>
        <ul>
          {chats.filter(chat => showArchived ? chat.archived : !chat.archived).map(chat => (
            <li key={chat.id} className="chat-item">
              {editingChat === chat.id ? (
                <div className="chat-edit">
                  <input
                    type="text"
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleSaveRename();
                      if (e.key === 'Escape') handleCancelRename();
                    }}
                    onBlur={handleSaveRename}
                    autoFocus
                    className="chat-edit-input"
                  />
                </div>
              ) : (
                <div className="chat-item-content">
                  <button
                    className={`chat-button ${currentChat?.id === chat.id ? 'active' : ''}`}
                    onClick={() => {
                      setCurrentChat(chat);
                      loadMessages(chat.id);
                    }}
                    title={chat.title}
                  >
                    {chat.title}
                  </button>
                  <div className="chat-options">
                    <button
                      className="options-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleDropdown(chat.id);
                      }}
                      title="Chat options"
                    >
                      ⋮
                    </button>
                    {openDropdown === chat.id && (
                      <div className="dropdown-menu">
                        <button
                          onClick={() => handleRenameChat(chat)}
                          className="dropdown-item"
                        >
                          <span className="dropdown-icon">✎</span> Rename
                        </button>
                        <button
                          onClick={() => handleShareChat(chat)}
                          className="dropdown-item"
                        >
                          <span className="dropdown-icon">⤴</span> Share
                        </button>
                        {chat.archived ? (
                          <button
                            onClick={() => handleUnarchiveChat(chat.id)}
                            className="dropdown-item"
                          >
                            <span className="dropdown-icon">↗</span> Unarchive
                          </button>
                        ) : (
                          <button
                            onClick={() => handleArchiveChat(chat.id)}
                            className="dropdown-item"
                          >
                            <span className="dropdown-icon">↘</span> Archive
                          </button>
                        )}
                        <button
                          onClick={() => setShowDeleteConfirm(chat.id)}
                          className="dropdown-item delete"
                        >
                          <span className="dropdown-icon">✕</span> Delete
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Delete confirmation modal */}
              {showDeleteConfirm === chat.id && (
                <div className="delete-confirm-overlay">
                  <div className="delete-confirm-modal">
                    <h3>Delete Chat?</h3>
                    <p>This action cannot be undone. All messages in this chat will be permanently deleted.</p>
                    <div className="delete-confirm-buttons">
                      <button
                        onClick={() => setShowDeleteConfirm(null)}
                        className="cancel-button"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => handleDeleteChat(chat.id)}
                        className="delete-button"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </li>
          ))}
        </ul>
      </aside>

      {/* Main Area */}
      <main className="main">
        {currentChat && displayMessages.length > 0 ? (
          <>
            <div className="chat-window">
              {displayMessages.map((msg, idx) => (
                <div key={idx} className={`message ${msg.role}`}>
                  {msg.content}
                </div>
              ))}
            </div>
            <div className="prompt-bar">
              <div className="textarea-wrapper">
                <textarea
                  value={prompt}
                  placeholder="Type your message..."
                  onChange={e => setPrompt(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />
                <button onClick={handleSend} disabled={isSubmitting}>
                  {isSubmitting ? '...' : '↑'}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="welcome-screen">
            <h2>Hello, What's on your mind?</h2>
            <div className="prompt-bar centered">
              <div className="textarea-wrapper">
                <textarea
                  value={prompt}
                  placeholder="Type your message..."
                  onChange={e => setPrompt(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />
                <button onClick={handleSend} disabled={isSubmitting}>
                  {isSubmitting ? '...' : '↑'}
                </button>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Right Sidebar */}
      <aside className="sidebar right">
        <h3>DB Tables</h3>
        <input
          type="text"
          placeholder="Search tables..."
          value={search}
          onChange={e => setSearch(e.target.value)}
        />
        <ul>
          {dbTables
            .filter(table => table.name.toLowerCase().includes(search.toLowerCase()))
            .map(table => (
              <li key={table.name}>
                <button
                  className={selectedTable === table.name ? 'active' : ''}
                  onClick={() =>
                    setSelectedTable(selectedTable === table.name ? null : table.name)
                  }
                >
                  {table.name}
                </button>
                {selectedTable === table.name && (
                  <ul className="columns-list">
                    {table.columns?.map(col => (
                      <li key={col.name || col}>{col.name || col}</li>
                    )) || <li>Loading columns...</li>}
                  </ul>
                )}
              </li>
            ))}
        </ul>
      </aside>
    </div>
  );
}

export default App;
