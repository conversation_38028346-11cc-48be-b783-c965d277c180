import { useState } from 'react';
import './App.css';

function App() {
  const [chatHistory, setChatHistory] = useState([]);
  const [selectedChat, setSelectedChat] = useState(null);
  const [prompt, setPrompt] = useState('');
  const [dbTables] = useState([
    { name: 'users', columns: ['id', 'name', 'email'] },
    { name: 'orders', columns: ['order_id', 'user_id', 'amount'] },
  ]);
  const [search, setSearch] = useState('');
  const [selectedTable, setSelectedTable] = useState(null);
  const [messages, setMessages] = useState({});

  const createNewChat = () => {
    const newChatId = Date.now();

    setChatHistory(prev => [
      { id: newChatId, title: 'New Chat' },
      ...prev,
    ]);

    setMessages(prev => ({
      ...prev,
      [newChatId]: [],
    }));

    // Do NOT select the chat yet — wait until user sends the first prompt
    setSelectedChat(null);
  };

  const handleSend = () => {
    if (!prompt.trim()) return;

    let currentChatId = selectedChat;

    // If no chat is selected, pick the latest one with no messages
    if (selectedChat === null) {
      const pendingChat = chatHistory.find(chat => !messages[chat.id] || messages[chat.id].length === 0);
      currentChatId = pendingChat ? pendingChat.id : Date.now();

      if (!pendingChat) {
        // If completely new chat, create it
        setChatHistory(prev => [
          { id: currentChatId, title: 'New Chat' },
          ...prev,
        ]);
      }

      setMessages(prev => ({
        ...prev,
        [currentChatId]: [{ role: 'user', content: prompt }],
      }));

      setSelectedChat(currentChatId);
      setPrompt('');
      return;
    }

    // Existing chat
    const updatedMessages = [
      ...(messages[currentChatId] || []),
      { role: 'user', content: prompt },
    ];

    setMessages(prev => ({
      ...prev,
      [currentChatId]: updatedMessages,
    }));

    // Update title after second user message
    const userMessages = updatedMessages.filter(m => m.role === 'user');
    if (userMessages.length === 2) {
      const first = userMessages[0].content.split(' ').slice(0, 3).join(' ');
      const second = userMessages[1].content.split(' ').slice(0, 3).join(' ');
      const newTitle = `${first} / ${second}`;
      setChatHistory(prev =>
        prev.map(chat =>
          chat.id === currentChatId ? { ...chat, title: newTitle } : chat
        )
      );
    }

    setPrompt('');
  };

  const displayMessages = selectedChat ? messages[selectedChat] || [] : [];

  return (
    <div className="layout">
      {/* Left Sidebar */}
      <aside className="sidebar left">
        <button onClick={createNewChat}>+ New Chat</button>
        <h3>Chats</h3>
        <ul>
          {chatHistory.map(chat => (
            <li key={chat.id}>
              <button
                className={selectedChat === chat.id ? 'active' : ''}
                onClick={() => setSelectedChat(chat.id)}
              >
                {chat.title}
              </button>
            </li>
          ))}
        </ul>
      </aside>

      {/* Main Area */}
      <main className="main">
        {selectedChat && messages[selectedChat]?.length > 0 ? (
          <>
            <div className="chat-window">
              {displayMessages.map((msg, idx) => (
                <div key={idx} className={`message ${msg.role}`}>
                  {msg.content}
                </div>
              ))}
            </div>
            <div className="prompt-bar">
              <div className="textarea-wrapper">
                <textarea
                  value={prompt}
                  placeholder="Type your message..."
                  onChange={e => setPrompt(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />
                <button onClick={handleSend}>
                  &#x2191;
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="welcome-screen">
            <h2>Hello, What's on your mind?</h2>
            <div className="prompt-bar centered">
              <div className="textarea-wrapper">
                <textarea
                  value={prompt}
                  placeholder="Type your message..."
                  onChange={e => setPrompt(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                />
                <button onClick={handleSend}>
                  &#x2191;
                </button>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Right Sidebar */}
      <aside className="sidebar right">
        <h3>DB Tables</h3>
        <input
          type="text"
          placeholder="Search tables..."
          value={search}
          onChange={e => setSearch(e.target.value)}
        />
        <ul>
          {dbTables
            .filter(table => table.name.includes(search))
            .map(table => (
              <li key={table.name}>
                <button
                  className={selectedTable === table.name ? 'active' : ''}
                  onClick={() =>
                    setSelectedTable(selectedTable === table.name ? null : table.name)
                  }
                >
                  {table.name}
                </button>
                {selectedTable === table.name && (
                  <ul className="columns-list">
                    {table.columns.map(col => (
                      <li key={col}>{col}</li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
        </ul>
      </aside>
    </div>
  );
}

export default App;
